#!/usr/bin/env python3
"""
Analizator hierarchii kategorii Ceneo - pobiera rzeczywistą strukturę
"""

import requests
from bs4 import BeautifulSoup
import re
import json

def analyze_ceneo_structure():
    """Analizuje strukturę kategorii na stronie Ceneo"""
    
    url = "https://www.ceneo.pl/SiteMap.aspx"
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'html.parser')
    
    print("=== ANALIZA STRUKTURY CENEO ===")
    
    # Znajdź wszystkie linki kategorii
    category_links = soup.find_all('a', href=re.compile(r'^/[A-Za-z0-9_-]+$'))
    
    # Analizuj kilka przykładowych kategorii
    examples = []
    
    for i, link in enumerate(category_links[:50]):  # Pierwsze 50 kategorii
        href = link.get('href', '').strip('/')
        text = link.get_text(strip=True)
        
        if not text or not href:
            continue
            
        # Wyciągnij liczbę produktów
        product_count_match = re.search(r'\((\d+)\)', text)
        product_count = int(product_count_match.group(1)) if product_count_match else 0
        
        # Usuń liczbę produktów z nazwy
        clean_name = re.sub(r'\s*\(\d+\)', '', text).strip()
        
        # Analizuj strukturę DOM
        dom_path = []
        current = link.parent
        while current and current.name != 'body':
            if current.name in ['ul', 'ol', 'div']:
                classes = ' '.join(current.get('class', []))
                dom_path.append(f"{current.name}({classes})")
            current = current.parent
        
        examples.append({
            'href': href,
            'name': clean_name,
            'product_count': product_count,
            'dom_path': ' > '.join(reversed(dom_path)),
            'dom_level': len([x for x in dom_path if x.startswith('ul') or x.startswith('ol')])
        })
    
    # Wyświetl przykłady
    print("\nPrzykładowe kategorie z analizą DOM:")
    for ex in examples[:20]:
        print(f"Nazwa: {ex['name']}")
        print(f"Produkty: {ex['product_count']}")
        print(f"DOM: {ex['dom_path']}")
        print(f"Poziom: {ex['dom_level']}")
        print("-" * 50)
    
    return examples

def get_specific_category_context(category_slug):
    """Pobiera kontekst konkretnej kategorii poprzez jej stronę"""
    
    url = f"https://www.ceneo.pl/{category_slug}"
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
    
    try:
        response = requests.get(url, headers=headers)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Znajdź breadcrumb (ścieżkę nawigacji)
        breadcrumb = soup.find('nav', class_=re.compile('breadcrumb|navigation'))
        if not breadcrumb:
            breadcrumb = soup.find('ol', class_=re.compile('breadcrumb'))
        if not breadcrumb:
            breadcrumb = soup.find('div', class_=re.compile('breadcrumb'))
        
        path = []
        if breadcrumb:
            links = breadcrumb.find_all('a')
            for link in links:
                text = link.get_text(strip=True)
                if text and text != 'Ceneo.pl':
                    path.append(text)
        
        return path
        
    except Exception as e:
        print(f"Błąd pobierania {category_slug}: {e}")
        return []

def analyze_specific_categories():
    """Analizuje konkretne kategorie problematyczne"""
    
    problem_categories = [
        'SUP',
        'Deszczownice_glowice_i_dysze', 
        'Dom',
        'Kitesurfing',
        'Sporty_wodne'
    ]
    
    print("\n=== ANALIZA KONKRETNYCH KATEGORII ===")
    
    results = {}
    
    for category in problem_categories:
        print(f"\nAnalizuję kategorię: {category}")
        path = get_specific_category_context(category)
        
        if path:
            print(f"Ścieżka breadcrumb: {' > '.join(path)}")
            results[category] = path
        else:
            print("Nie znaleziono ścieżki breadcrumb")
            results[category] = []
    
    return results

if __name__ == "__main__":
    # Analizuj strukturę
    structure = analyze_ceneo_structure()
    
    # Analizuj konkretne kategorie
    specific = analyze_specific_categories()
    
    # Zapisz wyniki
    with open('ceneo_structure_analysis.json', 'w', encoding='utf-8') as f:
        json.dump({
            'structure_examples': structure,
            'specific_categories': specific
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\nZapisano analizę do pliku: ceneo_structure_analysis.json")
