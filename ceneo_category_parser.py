#!/usr/bin/env python3
"""
Ceneo Category Parser and Google Taxonomy Mapper
Parsuje strukturę kategorii z Ceneo i mapuje je do Google Product Taxonomy
"""

import requests
import re
import json
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple, Optional
import csv

class CeneoGoogleTaxonomyMapper:
    def __init__(self):
        self.ceneo_categories = {}
        self.google_taxonomy = {}
        self.google_taxonomy_with_ids = {}
        self.mappings = []
        
    def fetch_ceneo_sitemap(self) -> str:
        """Pobiera mapę strony Ceneo"""
        url = "https://www.ceneo.pl/SiteMap.aspx"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.text
    
    def fetch_google_taxonomy(self) -> <PERSON><PERSON>[str, str]:
        """Pobiera Google Product Taxonomy"""
        base_url = "https://www.google.com/basepages/producttype/"
        
        # Taxonomy bez ID
        taxonomy_url = base_url + "taxonomy.en-US.txt"
        taxonomy_response = requests.get(taxonomy_url)
        taxonomy_response.raise_for_status()
        
        # Taxonomy z ID
        taxonomy_with_ids_url = base_url + "taxonomy-with-ids.en-US.txt"
        taxonomy_with_ids_response = requests.get(taxonomy_with_ids_url)
        taxonomy_with_ids_response.raise_for_status()
        
        return taxonomy_response.text, taxonomy_with_ids_response.text
    
    def parse_ceneo_categories(self, html_content: str) -> Dict:
        """Parsuje kategorie z HTML Ceneo"""
        soup = BeautifulSoup(html_content, 'html.parser')
        categories = {}
        
        # Znajdź sekcję ze szczegółowym podziałem
        detail_section = soup.find('div', string=re.compile('Szczegółowy podział'))
        if not detail_section:
            # Alternatywne wyszukiwanie
            detail_section = soup.find(text=re.compile('Szczegółowy podział'))
            if detail_section:
                detail_section = detail_section.parent
        
        if detail_section:
            # Znajdź wszystkie linki kategorii
            category_links = soup.find_all('a', href=re.compile(r'^/[A-Za-z0-9_-]+$'))
            
            for link in category_links:
                href = link.get('href', '').strip('/')
                text = link.get_text(strip=True)
                
                # Wyciągnij liczbę produktów z tekstu w nawiasach
                product_count_match = re.search(r'\((\d+)\)', text)
                product_count = int(product_count_match.group(1)) if product_count_match else 0
                
                # Usuń liczbę produktów z nazwy kategorii
                clean_name = re.sub(r'\s*\(\d+\)', '', text).strip()
                
                if clean_name and href:
                    categories[href] = {
                        'name': clean_name,
                        'url_slug': href,
                        'product_count': product_count,
                        'level': self._determine_category_level(link, soup)
                    }
        
        return categories
    
    def _determine_category_level(self, link_element, soup) -> int:
        """Określa poziom kategorii na podstawie struktury HTML"""
        # Sprawdź zagnieżdżenie w strukturze HTML
        parent = link_element.parent
        level = 0
        
        while parent and parent.name != 'body':
            if parent.name in ['ul', 'ol'] or 'category' in parent.get('class', []):
                level += 1
            parent = parent.parent
            
        return min(level, 4)  # Maksymalnie 4 poziomy
    
    def parse_google_taxonomy(self, taxonomy_text: str, taxonomy_with_ids_text: str):
        """Parsuje Google Product Taxonomy"""
        # Parsuj taxonomy bez ID
        for line in taxonomy_text.strip().split('\n'):
            if line.strip() and not line.startswith('#'):
                parts = line.split(' > ')
                category_path = ' > '.join(parts)
                self.google_taxonomy[category_path] = {
                    'path': parts,
                    'level': len(parts),
                    'full_path': category_path
                }
        
        # Parsuj taxonomy z ID
        for line in taxonomy_with_ids_text.strip().split('\n'):
            if line.strip() and not line.startswith('#'):
                # Format: ID - Category > Subcategory > ...
                match = re.match(r'^(\d+)\s*-\s*(.+)$', line)
                if match:
                    category_id = int(match.group(1))
                    category_path = match.group(2)
                    parts = category_path.split(' > ')
                    
                    self.google_taxonomy_with_ids[category_path] = {
                        'id': category_id,
                        'path': parts,
                        'level': len(parts),
                        'full_path': category_path
                    }
    
    def create_mapping(self) -> List[Dict]:
        """Tworzy mapowanie między kategoriami Ceneo a Google Taxonomy"""
        mappings = []
        
        for ceneo_slug, ceneo_data in self.ceneo_categories.items():
            ceneo_name = ceneo_data['name']
            best_matches = self._find_best_google_matches(ceneo_name, ceneo_data)
            
            for match in best_matches[:3]:  # Top 3 dopasowania
                mapping = {
                    'ceneo_slug': ceneo_slug,
                    'ceneo_name': ceneo_name,
                    'ceneo_product_count': ceneo_data['product_count'],
                    'ceneo_level': ceneo_data['level'],
                    'google_id': match['id'],
                    'google_path': match['full_path'],
                    'google_level': match['level'],
                    'confidence_score': match['score'],
                    'match_type': match['match_type']
                }
                mappings.append(mapping)
        
        return sorted(mappings, key=lambda x: x['confidence_score'], reverse=True)
    
    def _find_best_google_matches(self, ceneo_name: str, ceneo_data: Dict) -> List[Dict]:
        """Znajduje najlepsze dopasowania w Google Taxonomy"""
        matches = []
        ceneo_name_lower = ceneo_name.lower()
        
        # Słownik mapowań słów kluczowych PL -> EN
        keyword_mapping = {
            'deszczownice': ['shower heads', 'showers'],
            'głowice': ['heads', 'valves', 'faucet'],
            'dysze': ['nozzles', 'sprays'],
            'baterie': ['faucets', 'taps'],
            'armatura': ['plumbing', 'fixtures'],
            'hydraulika': ['plumbing'],
            'prysznic': ['shower'],
            'kabiny': ['enclosures', 'stalls'],
            'brodziki': ['shower bases', 'shower pans'],
            'wanny': ['bathtubs', 'baths'],
            'umywalki': ['sinks', 'basins'],
            'toaleta': ['toilet', 'wc'],
            'bidety': ['bidets'],
            'spłuczki': ['flush tanks'],
            'zawory': ['valves'],
            'syfony': ['drains', 'traps']
        }
        
        # Znajdź słowa kluczowe w nazwie Ceneo
        ceneo_keywords = []
        for pl_word, en_words in keyword_mapping.items():
            if pl_word in ceneo_name_lower:
                ceneo_keywords.extend(en_words)
        
        # Przeszukaj Google Taxonomy
        for google_path, google_data in self.google_taxonomy_with_ids.items():
            google_path_lower = google_path.lower()
            score = 0
            match_type = 'none'
            
            # Dokładne dopasowanie nazwy
            if ceneo_name_lower in google_path_lower:
                score += 100
                match_type = 'exact_name'
            
            # Dopasowanie słów kluczowych
            keyword_matches = 0
            for keyword in ceneo_keywords:
                if keyword.lower() in google_path_lower:
                    keyword_matches += 1
                    score += 20
            
            if keyword_matches > 0:
                if match_type == 'none':
                    match_type = 'keyword'
                else:
                    match_type += '_keyword'
            
            # Bonus za odpowiedni poziom hierarchii
            level_diff = abs(google_data['level'] - ceneo_data.get('level', 2))
            if level_diff <= 1:
                score += 10
            
            # Bonus za kategorie hydrauliczne/budowlane
            if any(term in google_path_lower for term in ['plumbing', 'hardware', 'home improvement']):
                score += 15
                if match_type == 'none':
                    match_type = 'category'
            
            if score > 0:
                matches.append({
                    'id': google_data['id'],
                    'full_path': google_path,
                    'level': google_data['level'],
                    'score': score,
                    'match_type': match_type
                })
        
        return sorted(matches, key=lambda x: x['score'], reverse=True)
    
    def save_mappings_to_csv(self, mappings: List[Dict], filename: str = 'ceneo_google_taxonomy_mapping.csv'):
        """Zapisuje mapowania do pliku CSV"""
        if not mappings:
            print("Brak mapowań do zapisania")
            return
            
        fieldnames = mappings[0].keys()
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(mappings)
        
        print(f"Zapisano {len(mappings)} mapowań do pliku {filename}")
    
    def run_full_mapping(self):
        """Uruchamia pełny proces mapowania"""
        print("Pobieranie danych Ceneo...")
        ceneo_html = self.fetch_ceneo_sitemap()
        
        print("Parsowanie kategorii Ceneo...")
        self.ceneo_categories = self.parse_ceneo_categories(ceneo_html)
        print(f"Znaleziono {len(self.ceneo_categories)} kategorii Ceneo")
        
        print("Pobieranie Google Taxonomy...")
        taxonomy_text, taxonomy_with_ids_text = self.fetch_google_taxonomy()
        
        print("Parsowanie Google Taxonomy...")
        self.parse_google_taxonomy(taxonomy_text, taxonomy_with_ids_text)
        print(f"Załadowano {len(self.google_taxonomy_with_ids)} kategorii Google")
        
        print("Tworzenie mapowań...")
        self.mappings = self.create_mapping()
        print(f"Utworzono {len(self.mappings)} mapowań")
        
        print("Zapisywanie wyników...")
        self.save_mappings_to_csv(self.mappings)
        
        return self.mappings

if __name__ == "__main__":
    mapper = CeneoGoogleTaxonomyMapper()
    mappings = mapper.run_full_mapping()
    
    # Wyświetl przykładowe mapowania
    print("\n=== PRZYKŁADOWE MAPOWANIA ===")
    for mapping in mappings[:10]:
        print(f"Ceneo: {mapping['ceneo_name']}")
        print(f"Google: {mapping['google_path']} (ID: {mapping['google_id']})")
        print(f"Pewność: {mapping['confidence_score']}, Typ: {mapping['match_type']}")
        print("-" * 50)
