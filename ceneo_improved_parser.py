#!/usr/bin/env python3
"""
Poprawiony parser kategorii Ceneo z rzeczywistą hierarchią
"""

import requests
import re
import json
import csv
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time

class ImprovedCeneoMapper:
    def __init__(self):
        self.ceneo_categories = {}
        self.google_taxonomy_with_ids = {}
        self.category_hierarchies = {}
        
    def fetch_ceneo_sitemap(self) -> str:
        """Pobiera mapę strony Ceneo"""
        url = "https://www.ceneo.pl/SiteMap.aspx"
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.text
    
    def fetch_google_taxonomy(self) -> str:
        """Pobiera Google Product Taxonomy z ID"""
        url = "https://www.google.com/basepages/producttype/taxonomy-with-ids.en-US.txt"
        response = requests.get(url)
        response.raise_for_status()
        return response.text
    
    def get_category_hierarchy(self, category_slug: str) -> List[str]:
        """Pobiera rzeczywistą hierarchię kategorii z breadcrumb"""
        if category_slug in self.category_hierarchies:
            return self.category_hierarchies[category_slug]
            
        url = f"https://www.ceneo.pl/{category_slug}"
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Znajdź breadcrumb
            breadcrumb_selectors = [
                'nav[class*="breadcrumb"]',
                'ol[class*="breadcrumb"]', 
                'div[class*="breadcrumb"]',
                '.breadcrumb',
                '[data-testid="breadcrumb"]'
            ]
            
            breadcrumb = None
            for selector in breadcrumb_selectors:
                breadcrumb = soup.select_one(selector)
                if breadcrumb:
                    break
            
            path = []
            if breadcrumb:
                links = breadcrumb.find_all('a')
                for link in links:
                    text = link.get_text(strip=True)
                    if text and text not in ['Ceneo.pl', 'Ceneo']:
                        path.append(text)
                        
                # Dodaj aktualną kategorię jeśli nie ma linku
                if not path or category_slug not in [p.lower().replace(' ', '_').replace(',', '') for p in path]:
                    # Spróbuj znaleźć h1 z nazwą kategorii
                    h1 = soup.find('h1')
                    if h1:
                        current_name = h1.get_text(strip=True)
                        if current_name and current_name not in path:
                            path.append(current_name)
            
            self.category_hierarchies[category_slug] = path
            return path
            
        except Exception as e:
            print(f"Błąd pobierania hierarchii dla {category_slug}: {e}")
            self.category_hierarchies[category_slug] = []
            return []
    
    def parse_ceneo_categories_with_hierarchy(self, html_content: str) -> Dict:
        """Parsuje kategorie z rzeczywistą hierarchią"""
        soup = BeautifulSoup(html_content, 'html.parser')
        categories = {}
        
        # Znajdź wszystkie linki kategorii
        category_links = soup.find_all('a', href=re.compile(r'^/[A-Za-z0-9_-]+$'))
        
        print(f"Znaleziono {len(category_links)} linków kategorii")
        print("Pobieranie hierarchii kategorii...")
        
        # Pobierz hierarchie dla wszystkich kategorii (z ograniczeniem)
        processed = 0
        batch_size = 50  # Przetwarzaj w partiach
        
        for i in range(0, min(len(category_links), 500), batch_size):  # Maksymalnie 500 kategorii
            batch = category_links[i:i+batch_size]
            
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = {}
                
                for link in batch:
                    href = link.get('href', '').strip('/')
                    text = link.get_text(strip=True)
                    
                    if not text or not href:
                        continue
                        
                    future = executor.submit(self.get_category_hierarchy, href)
                    futures[future] = (href, text, link)
                
                for future in as_completed(futures):
                    href, text, link = futures[future]
                    hierarchy = future.result()
                    
                    # Wyciągnij liczbę produktów
                    product_count_match = re.search(r'\((\d+)\)', text)
                    product_count = int(product_count_match.group(1)) if product_count_match else 0
                    
                    # Usuń liczbę produktów z nazwy
                    clean_name = re.sub(r'\s*\(\d+\)', '', text).strip()
                    
                    categories[href] = {
                        'name': clean_name,
                        'url_slug': href,
                        'product_count': product_count,
                        'level': len(hierarchy),
                        'main_category': hierarchy[0] if hierarchy else clean_name,
                        'category_path': hierarchy,
                        'parent_categories': hierarchy[:-1] if len(hierarchy) > 1 else [],
                        'full_context': ' > '.join(hierarchy) if hierarchy else clean_name
                    }
                    
                    processed += 1
                    if processed % 10 == 0:
                        print(f"Przetworzono {processed} kategorii...")
            
            # Krótka przerwa między partiami
            time.sleep(1)
        
        print(f"Zakończono. Przetworzono {len(categories)} kategorii z hierarchią.")
        return categories
    
    def parse_google_taxonomy(self, taxonomy_with_ids_text: str):
        """Parsuje Google Product Taxonomy"""
        for line in taxonomy_with_ids_text.strip().split('\n'):
            if line.strip() and not line.startswith('#'):
                match = re.match(r'^(\d+)\s*-\s*(.+)$', line)
                if match:
                    category_id = int(match.group(1))
                    category_path = match.group(2)
                    parts = category_path.split(' > ')
                    
                    self.google_taxonomy_with_ids[category_path] = {
                        'id': category_id,
                        'path': parts,
                        'level': len(parts),
                        'full_path': category_path
                    }
    
    def find_best_google_matches_improved(self, ceneo_name: str, ceneo_data: Dict) -> List[Dict]:
        """Ulepszone dopasowanie z uwzględnieniem pełnej hierarchii"""
        matches = []
        ceneo_name_lower = ceneo_name.lower()
        
        # Pobierz pełny kontekst
        full_context = ceneo_data.get('full_context', '').lower()
        category_path = ceneo_data.get('category_path', [])
        main_category = ceneo_data.get('main_category', '').lower()
        
        # Rozszerzony słownik mapowań z kontekstem
        context_mappings = {
            # Sport i rekreacja
            'sport i rekreacja': ['sporting goods', 'sports', 'recreation'],
            'sporty wodne': ['water sports', 'boating', 'swimming', 'aquatic'],
            'sup': ['paddle boarding', 'stand up paddle', 'paddleboard'],
            'kitesurfing': ['kitesurfing', 'kite boarding'],
            'pływanie': ['swimming', 'pool'],
            'nurkowanie': ['diving', 'scuba'],
            
            # Budowa i remont
            'budowa i remont': ['hardware', 'building', 'construction', 'home improvement'],
            'armatura i hydraulika': ['plumbing', 'fixtures', 'faucets'],
            'prysznic i akcesoria': ['shower', 'bathroom'],
            'deszczownice': ['shower heads'],
            'głowice': ['heads', 'valves'],
            'dysze': ['nozzles', 'sprays'],
            'baterie': ['faucets', 'taps'],
            'kabiny prysznicowe': ['shower enclosures', 'shower stalls'],
            
            # Dom i ogród
            'dom i ogród': ['home & garden', 'household'],
            'ogród': ['garden', 'outdoor', 'yard'],
            'meble': ['furniture'],
            'kuchnia': ['kitchen'],
            'łazienka': ['bathroom'],
            
            # Elektronika
            'elektronika': ['electronics'],
            'rtv i agd': ['electronics', 'appliances', 'audio', 'video'],
            'audio': ['audio'],
            'video': ['video'],
            
            # Inne
            'zdrowie': ['health & beauty', 'health care'],
            'uroda': ['beauty', 'personal care'],
            'odzież': ['apparel & accessories', 'clothing'],
            'motoryzacja': ['vehicles & parts', 'automotive'],
            'biuro i firma': ['business & industrial', 'office']
        }
        
        # Zbierz wszystkie słowa kluczowe z kontekstu
        context_keywords = []
        for path_element in category_path:
            path_lower = path_element.lower()
            for context_key, keywords in context_mappings.items():
                if context_key in path_lower or any(word in path_lower for word in context_key.split()):
                    context_keywords.extend(keywords)
        
        # Dodaj słowa kluczowe z nazwy kategorii
        name_keywords = []
        for context_key, keywords in context_mappings.items():
            if context_key in ceneo_name_lower:
                name_keywords.extend(keywords)
        
        # Przeszukaj Google Taxonomy
        for google_path, google_data in self.google_taxonomy_with_ids.items():
            google_path_lower = google_path.lower()
            score = 0
            match_type = 'none'
            
            # Dokładne dopasowanie nazwy
            if ceneo_name_lower in google_path_lower and len(ceneo_name_lower) > 3:
                score += 100
                match_type = 'exact_name'
            
            # Dopasowanie słów kluczowych z nazwy
            name_matches = sum(1 for keyword in name_keywords if keyword.lower() in google_path_lower)
            score += name_matches * 30
            
            # Dopasowanie kontekstowe
            context_matches = sum(1 for keyword in context_keywords if keyword.lower() in google_path_lower)
            score += context_matches * 20
            
            # Bonus za dopasowanie głównej kategorii
            if main_category:
                main_keywords = context_mappings.get(main_category, [])
                main_matches = sum(1 for keyword in main_keywords if keyword.lower() in google_path_lower)
                score += main_matches * 25
            
            # Określ typ dopasowania
            if name_matches > 0 and context_matches > 0:
                match_type = 'name_context'
            elif name_matches > 0:
                match_type = 'name_keyword'
            elif context_matches > 0:
                match_type = 'context'
            elif match_type == 'none':
                match_type = 'weak'
            
            # Bonus za odpowiedni poziom hierarchii
            level_diff = abs(google_data['level'] - ceneo_data.get('level', 2))
            if level_diff <= 1:
                score += 15
            elif level_diff <= 2:
                score += 5
            
            # Specjalne przypadki - eliminuj błędne dopasowania
            if 'dom' in ceneo_name_lower and 'condom' in google_path_lower:
                score = 0
            
            if score > 0:
                matches.append({
                    'id': google_data['id'],
                    'full_path': google_path,
                    'level': google_data['level'],
                    'score': score,
                    'match_type': match_type,
                    'name_matches': name_matches,
                    'context_matches': context_matches
                })
        
        return sorted(matches, key=lambda x: (x['score'], x['name_matches'], x['context_matches']), reverse=True)
    
    def create_improved_mapping(self) -> List[Dict]:
        """Tworzy ulepszone mapowanie"""
        mappings = []
        
        for ceneo_slug, ceneo_data in self.ceneo_categories.items():
            ceneo_name = ceneo_data['name']
            best_matches = self.find_best_google_matches_improved(ceneo_name, ceneo_data)
            
            for match in best_matches[:3]:  # Top 3 dopasowania
                mapping = {
                    'ceneo_slug': ceneo_slug,
                    'ceneo_name': ceneo_name,
                    'ceneo_full_context': ceneo_data.get('full_context', ''),
                    'ceneo_product_count': ceneo_data['product_count'],
                    'ceneo_level': ceneo_data['level'],
                    'google_id': match['id'],
                    'google_path': match['full_path'],
                    'google_level': match['level'],
                    'confidence_score': match['score'],
                    'match_type': match['match_type']
                }
                mappings.append(mapping)
        
        return sorted(mappings, key=lambda x: x['confidence_score'], reverse=True)
    
    def run_improved_mapping(self):
        """Uruchamia ulepszone mapowanie"""
        print("Pobieranie danych Ceneo...")
        ceneo_html = self.fetch_ceneo_sitemap()
        
        print("Parsowanie kategorii Ceneo z hierarchią...")
        self.ceneo_categories = self.parse_ceneo_categories_with_hierarchy(ceneo_html)
        print(f"Znaleziono {len(self.ceneo_categories)} kategorii Ceneo")
        
        print("Pobieranie Google Taxonomy...")
        taxonomy_with_ids_text = self.fetch_google_taxonomy()
        
        print("Parsowanie Google Taxonomy...")
        self.parse_google_taxonomy(taxonomy_with_ids_text)
        print(f"Załadowano {len(self.google_taxonomy_with_ids)} kategorii Google")
        
        print("Tworzenie ulepszonych mapowań...")
        mappings = self.create_improved_mapping()
        print(f"Utworzono {len(mappings)} mapowań")
        
        # Zapisz wyniki
        filename = 'ceneo_google_improved_mapping.csv'
        if mappings:
            fieldnames = mappings[0].keys()
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(mappings)
            print(f"Zapisano {len(mappings)} mapowań do pliku {filename}")
        
        return mappings

if __name__ == "__main__":
    mapper = ImprovedCeneoMapper()
    mappings = mapper.run_improved_mapping()
    
    # Wyświetl przykładowe mapowania
    print("\n=== PRZYKŁADOWE ULEPSZONE MAPOWANIA ===")
    for mapping in mappings[:10]:
        print(f"Ceneo: {mapping['ceneo_name']}")
        print(f"Kontekst: {mapping['ceneo_full_context']}")
        print(f"Google: {mapping['google_path']} (ID: {mapping['google_id']})")
        print(f"Pewność: {mapping['confidence_score']}, Typ: {mapping['match_type']}")
        print("-" * 70)
