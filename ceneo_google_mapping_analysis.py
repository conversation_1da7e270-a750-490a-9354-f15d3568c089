#!/usr/bin/env python3
"""
Analiza mapowania kategorii Ceneo do Google Product Taxonomy
"""

import pandas as pd
import json
from collections import defaultdict

def analyze_mappings():
    """Analizuje wyniki mapowania"""
    
    # Wczytaj dane
    df = pd.read_csv('ceneo_google_taxonomy_mapping.csv')
    
    print("=== ANALIZA MAPOWANIA CENEO -> GOOGLE TAXONOMY ===\n")
    
    # Podstawowe statystyki
    print(f"Łączna liczba mapowań: {len(df)}")
    print(f"Liczba unikalnych kategorii Ceneo: {df['ceneo_name'].nunique()}")
    print(f"Liczba unikalnych kategorii Google: {df['google_id'].nunique()}")
    print(f"Średnia pewność dopasowania: {df['confidence_score'].mean():.2f}")
    print()
    
    # Analiza typów dopasowań
    print("=== TYPY DOPASOWAŃ ===")
    match_types = df['match_type'].value_counts()
    for match_type, count in match_types.items():
        percentage = (count / len(df)) * 100
        print(f"{match_type}: {count} ({percentage:.1f}%)")
    print()
    
    # Top kategorie Ceneo z najlepszymi dopasowaniami
    print("=== TOP 20 NAJLEPSZYCH DOPASOWAŃ ===")
    top_mappings = df.nlargest(20, 'confidence_score')
    
    for idx, row in top_mappings.iterrows():
        print(f"Ceneo: {row['ceneo_name']}")
        print(f"Google: {row['google_path']} (ID: {row['google_id']})")
        print(f"Pewność: {row['confidence_score']}, Typ: {row['match_type']}")
        print("-" * 80)
    
    # Analiza kategorii hydraulicznych
    print("\n=== KATEGORIE HYDRAULICZNE I ARMATURA ===")
    hydraulic_keywords = ['Deszczownice', 'głowice', 'dysze', 'Armatura', 'Hydraulika', 
                         'Prysznic', 'Baterie', 'Kabiny', 'Brodziki', 'Wanny', 'Umywalki']
    
    hydraulic_filter = df['ceneo_name'].str.contains('|'.join(hydraulic_keywords), case=False, na=False)
    hydraulic_df = df[hydraulic_filter].sort_values('confidence_score', ascending=False)
    
    print(f"Znaleziono {len(hydraulic_df)} mapowań dla kategorii hydraulicznych")
    print("\nNajlepsze dopasowania:")
    
    for idx, row in hydraulic_df.head(10).iterrows():
        print(f"Ceneo: {row['ceneo_name']}")
        print(f"Google: {row['google_path']} (ID: {row['google_id']})")
        print(f"Pewność: {row['confidence_score']}")
        print()
    
    # Konkretna analiza dla "Deszczownice, głowice i dysze"
    print("=== ANALIZA: 'Deszczownice, głowice i dysze' ===")
    target_category = df[df['ceneo_name'] == 'Deszczownice, głowice i dysze'].sort_values('confidence_score', ascending=False)
    
    if not target_category.empty:
        print("Najlepsze dopasowania:")
        for idx, row in target_category.head(5).iterrows():
            print(f"Google: {row['google_path']}")
            print(f"ID: {row['google_id']}")
            print(f"Pewność: {row['confidence_score']}")
            print(f"Typ dopasowania: {row['match_type']}")
            print()
        
        # Rekomendacja
        best_match = target_category.iloc[0]
        print("=== REKOMENDACJA ===")
        print(f"Najlepsze dopasowanie dla kategorii Ceneo 'Deszczownice, głowice i dysze':")
        print(f"Google Taxonomy: {best_match['google_path']}")
        print(f"Google ID: {best_match['google_id']}")
        print(f"Pewność dopasowania: {best_match['confidence_score']}")
        print()
    
    # Analiza kategorii Google najczęściej używanych
    print("=== NAJCZĘŚCIEJ UŻYWANE KATEGORIE GOOGLE ===")
    google_usage = df.groupby(['google_id', 'google_path']).size().sort_values(ascending=False)
    
    print("Top 15 kategorii Google z największą liczbą dopasowań:")
    for (google_id, google_path), count in google_usage.head(15).items():
        print(f"ID {google_id}: {google_path} ({count} dopasowań)")
    print()
    
    # Eksport najlepszych dopasowań do JSON
    export_best_mappings(df)
    
    return df

def export_best_mappings(df):
    """Eksportuje najlepsze dopasowania do JSON"""
    
    # Weź tylko najlepsze dopasowanie dla każdej kategorii Ceneo
    best_mappings = df.loc[df.groupby('ceneo_name')['confidence_score'].idxmax()]
    
    # Przygotuj dane do eksportu
    export_data = []
    for _, row in best_mappings.iterrows():
        export_data.append({
            'ceneo_category': row['ceneo_name'],
            'ceneo_slug': row['ceneo_slug'],
            'google_taxonomy_id': int(row['google_id']),
            'google_taxonomy_path': row['google_path'],
            'confidence_score': float(row['confidence_score']),
            'match_type': row['match_type']
        })
    
    # Zapisz do JSON
    with open('ceneo_google_best_mappings.json', 'w', encoding='utf-8') as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    print(f"Wyeksportowano {len(export_data)} najlepszych dopasowań do pliku 'ceneo_google_best_mappings.json'")

def create_specific_category_report():
    """Tworzy szczegółowy raport dla konkretnych kategorii"""
    
    df = pd.read_csv('ceneo_google_taxonomy_mapping.csv')
    
    # Kategorie do szczegółowej analizy
    target_categories = [
        'Deszczownice, głowice i dysze',
        'Baterie łazienkowe',
        'Kabiny prysznicowe',
        'Armatura i hydraulika',
        'Prysznic i akcesoria'
    ]
    
    report = {}
    
    for category in target_categories:
        category_data = df[df['ceneo_name'] == category].sort_values('confidence_score', ascending=False)
        
        if not category_data.empty:
            report[category] = []
            for _, row in category_data.head(3).iterrows():  # Top 3 dla każdej kategorii
                report[category].append({
                    'google_id': int(row['google_id']),
                    'google_path': row['google_path'],
                    'confidence_score': float(row['confidence_score']),
                    'match_type': row['match_type']
                })
    
    # Zapisz raport
    with open('specific_categories_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("Utworzono szczegółowy raport dla wybranych kategorii: 'specific_categories_report.json'")
    
    return report

if __name__ == "__main__":
    # Uruchom analizę
    df = analyze_mappings()
    
    # Utwórz szczegółowy raport
    specific_report = create_specific_category_report()
    
    print("\n=== PODSUMOWANIE ===")
    print("Utworzono następujące pliki:")
    print("1. ceneo_google_taxonomy_mapping.csv - pełne mapowanie")
    print("2. ceneo_google_best_mappings.json - najlepsze dopasowania")
    print("3. specific_categories_report.json - raport dla wybranych kategorii")
